#!/usr/bin/env node

/**
 * Test script for message read status feature
 * 
 * This script tests the basic functionality of the message read status API endpoints
 * Run with: node scripts/test-read-status.js
 */

const https = require('https');
const http = require('http');

// Configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';
const TEST_TOKEN = process.env.TEST_TOKEN || 'your-test-jwt-token';

// Helper function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const protocol = options.hostname === 'localhost' ? http : https;
    
    const req = protocol.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            body: jsonBody
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Test functions
async function testGetReadStatus() {
  console.log('\n🧪 Testing GET /api/v1/chat-message/read-status...');
  
  try {
    const url = new URL(`${BASE_URL}/api/v1/chat-message/read-status`);
    url.searchParams.set('messageId', '1');
    
    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: url.pathname + url.search,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    };

    const response = await makeRequest(options);
    
    if (response.status === 200) {
      console.log('✅ Read status API working');
      console.log('📊 Response:', JSON.stringify(response.body, null, 2));
    } else if (response.status === 401) {
      console.log('⚠️  Authentication required - please set TEST_TOKEN environment variable');
    } else if (response.status === 404) {
      console.log('⚠️  Message not found - this is expected if no messages exist');
    } else {
      console.log(`❌ Unexpected status: ${response.status}`);
      console.log('📄 Response:', response.body);
    }
  } catch (error) {
    console.log('❌ Error testing read status API:', error.message);
  }
}

async function testGetMessagesWithReadStatus() {
  console.log('\n🧪 Testing GET /api/v1/chat-message with includeReadStatus...');
  
  try {
    const url = new URL(`${BASE_URL}/api/v1/chat-message`);
    url.searchParams.set('chatId', '1');
    url.searchParams.set('includeReadStatus', 'true');
    url.searchParams.set('limit', '5');
    
    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: url.pathname + url.search,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    };

    const response = await makeRequest(options);
    
    if (response.status === 200) {
      console.log('✅ Messages with read status API working');
      if (response.body.messages && response.body.messages.length > 0) {
        const firstMessage = response.body.messages[0];
        console.log('📊 First message read status:', {
          id: firstMessage.id,
          hasReadStatus: !!firstMessage.readStatus,
          readCount: firstMessage.readStatus?.readCount || 0,
          unreadCount: firstMessage.readStatus?.unreadCount || 0
        });
      } else {
        console.log('📄 No messages found in chat');
      }
    } else if (response.status === 401) {
      console.log('⚠️  Authentication required - please set TEST_TOKEN environment variable');
    } else if (response.status === 403) {
      console.log('⚠️  Access denied - user not in chat or chat not found');
    } else {
      console.log(`❌ Unexpected status: ${response.status}`);
      console.log('📄 Response:', response.body);
    }
  } catch (error) {
    console.log('❌ Error testing messages API:', error.message);
  }
}

async function testMarkMessageAsRead() {
  console.log('\n🧪 Testing POST /api/v1/chat-message/mark-read...');
  
  try {
    const url = new URL(`${BASE_URL}/api/v1/chat-message/mark-read`);
    
    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: url.pathname,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    };

    const data = {
      messageId: 1
    };

    const response = await makeRequest(options, data);
    
    if (response.status === 200) {
      console.log('✅ Mark message as read API working');
      console.log('📊 Response:', response.body.message);
    } else if (response.status === 401) {
      console.log('⚠️  Authentication required - please set TEST_TOKEN environment variable');
    } else if (response.status === 400) {
      console.log('⚠️  Bad request - possibly trying to mark own message as read');
    } else if (response.status === 404) {
      console.log('⚠️  Message not found - this is expected if no messages exist');
    } else {
      console.log(`❌ Unexpected status: ${response.status}`);
      console.log('📄 Response:', response.body);
    }
  } catch (error) {
    console.log('❌ Error testing mark as read API:', error.message);
  }
}

async function testChatReadStatus() {
  console.log('\n🧪 Testing GET /api/v1/chat-message/read-status for chat...');
  
  try {
    const url = new URL(`${BASE_URL}/api/v1/chat-message/read-status`);
    url.searchParams.set('chatId', '1');
    url.searchParams.set('limit', '5');
    
    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: url.pathname + url.search,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    };

    const response = await makeRequest(options);
    
    if (response.status === 200) {
      console.log('✅ Chat read status API working');
      if (response.body.messageReadStatuses && response.body.messageReadStatuses.length > 0) {
        console.log(`📊 Found read status for ${response.body.messageReadStatuses.length} messages`);
        const firstStatus = response.body.messageReadStatuses[0];
        console.log('📊 First message status:', {
          messageId: firstStatus.messageId,
          readCount: firstStatus.readCount,
          unreadCount: firstStatus.unreadCount,
          totalParticipants: firstStatus.totalParticipants
        });
      } else {
        console.log('📄 No message read statuses found');
      }
    } else if (response.status === 401) {
      console.log('⚠️  Authentication required - please set TEST_TOKEN environment variable');
    } else if (response.status === 403) {
      console.log('⚠️  Access denied - user not in chat or chat not found');
    } else {
      console.log(`❌ Unexpected status: ${response.status}`);
      console.log('📄 Response:', response.body);
    }
  } catch (error) {
    console.log('❌ Error testing chat read status API:', error.message);
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Message Read Status API Tests');
  console.log(`🌐 Base URL: ${BASE_URL}`);
  console.log(`🔑 Using token: ${TEST_TOKEN ? 'Set' : 'Not set'}`);
  
  if (!TEST_TOKEN || TEST_TOKEN === 'your-test-jwt-token') {
    console.log('\n⚠️  Warning: Please set a valid TEST_TOKEN environment variable');
    console.log('   Example: TEST_TOKEN=your-jwt-token node scripts/test-read-status.js');
  }

  await testGetReadStatus();
  await testGetMessagesWithReadStatus();
  await testMarkMessageAsRead();
  await testChatReadStatus();
  
  console.log('\n✨ Test run completed!');
  console.log('\n📝 Notes:');
  console.log('   - Some tests may show warnings if no test data exists');
  console.log('   - Authentication errors are expected without a valid token');
  console.log('   - Create some test messages and chats for full testing');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testGetReadStatus,
  testGetMessagesWithReadStatus,
  testMarkMessageAsRead,
  testChatReadStatus,
  runTests
};
